import sqlite3
import os
from datetime import datetime
import logging
from typing import List, Dict, Optional

logger = logging.getLogger(__name__)


class Database:
    def __init__(self, config: Dict[str, any]):
        # SQLite数据库文件路径
        self.db_path = config.get('database', 'binance_data.db')
        self.connection = None
        self.cursor = None

    async def connect(self):
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
            self.cursor = self.connection.cursor()
            logger.info(f'SQLite数据库连接成功: {self.db_path}')
            await self.create_tables()
        except Exception as e:
            logger.error(f'数据库连接失败: {e}')
            raise

    async def create_tables(self):
        """创建数据表"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS kline_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            symbol TEXT NOT NULL,
            open_time DATETIME NOT NULL,
            open_price REAL NOT NULL,
            high_price REAL NOT NULL,
            low_price REAL NOT NULL,
            close_price REAL NOT NULL,
            volume REAL NOT NULL,
            close_time DATETIME NOT NULL,
            quote_volume REAL NOT NULL,
            trades INTEGER NOT NULL,
            taker_buy_base_volume REAL NOT NULL,
            taker_buy_quote_volume REAL NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(symbol, open_time)
        )"""
        
        index_sql = """
        CREATE INDEX IF NOT EXISTS idx_symbol_time 
        ON kline_data(symbol, open_time)
        """
        
        try:
            self.cursor.execute(create_table_sql)
            self.cursor.execute(index_sql)
            self.connection.commit()
            logger.info('数据表创建成功')
        except Exception as e:
            logger.error(f'创建数据表失败: {e}')
            raise

    async def insert_kline_data(self, symbol: str, kline_data: List[Dict]):
        """插入K线数据 - 使用批量插入优化性能"""
        if not kline_data:
            return
            
        insert_sql = """
        INSERT OR REPLACE INTO kline_data (
            symbol, open_time, open_price, high_price, low_price, close_price,
            volume, close_time, quote_volume, trades, taker_buy_base_volume, taker_buy_quote_volume
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            # 准备批量数据
            batch_data = []
            for kline in kline_data:
                values = (
                    symbol,
                    kline['open_time'],
                    kline['open'],
                    kline['high'],
                    kline['low'],
                    kline['close'],
                    kline['volume'],
                    kline['close_time'],
                    kline['quote_volume'],
                    kline['trades'],
                    kline['taker_buy_base_volume'],
                    kline['taker_buy_quote_volume']
                )
                batch_data.append(values)
            
            # 批量插入
            self.cursor.executemany(insert_sql, batch_data)
            self.connection.commit()
            logger.info(f'批量插入{len(kline_data)}条{symbol}数据成功')
            
        except Exception as e:
            self.connection.rollback()
            logger.error(f'批量插入{symbol}数据失败: {e}')
            raise

    async def get_latest_kline(self, symbol: str) -> Optional[Dict]:
        """获取最新的K线数据"""
        query = """
        SELECT * FROM kline_data 
        WHERE symbol = ? 
        ORDER BY open_time DESC 
        LIMIT 1
        """
        
        try:
            self.cursor.execute(query, (symbol,))
            result = self.cursor.fetchone()
            return dict(result) if result else None
        except Exception as e:
            logger.error(f'查询最新数据失败: {e}')
            raise

    async def get_kline_data_count(self, symbol: str) -> int:
        """获取K线数据总数"""
        query = "SELECT COUNT(*) as count FROM kline_data WHERE symbol = ?"
        
        try:
            self.cursor.execute(query, (symbol,))
            result = self.cursor.fetchone()
            return result[0] if result else 0
        except Exception as e:
            logger.error(f'查询数据总数失败: {e}')
            raise

    async def clear_old_data(self, symbol: str, days: int = 30):
        """清理旧数据"""
        query = """
        DELETE FROM kline_data 
        WHERE symbol = ? AND open_time < datetime('now', '-' || ? || ' days')
        """
        
        try:
            self.cursor.execute(query, (symbol, days))
            deleted_count = self.cursor.rowcount
            self.connection.commit()
            logger.info(f'清理了{deleted_count}条旧数据')
            return deleted_count
        except Exception as e:
            self.connection.rollback()
            logger.error(f'清理旧数据失败: {e}')
            raise

    async def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
            logger.info('数据库连接已关闭')
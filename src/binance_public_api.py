import requests
import time
from datetime import datetime
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class BinancePublicAPI:
    """币安公开API - 无需认证即可获取市场数据"""
    
    def __init__(self):
        self.base_url = 'https://api.binance.com'
        self.fallback_urls = [
            'https://api1.binance.com',
            'https://api2.binance.com',
            'https://api3.binance.com',
            'https://api4.binance.com'
        ]
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """发送API请求，支持多个备选URL"""
        urls_to_try = [self.base_url] + self.fallback_urls
        
        for i, url in enumerate(urls_to_try):
            try:
                logger.info(f'尝试API端点: {url}')
                response = self.session.get(
                    f'{url}{endpoint}',
                    params=params,
                    timeout=10
                )
                
                if response.status_code == 200:
                    logger.info(f'✅ 成功从 {url} 获取数据')
                    return response.json()
                else:
                    logger.error(f'API返回错误: {response.status_code} - {response.text}')
                    
            except Exception as e:
                logger.error(f'❌ {url} 失败: {str(e)}')
                
                if i == len(urls_to_try) - 1:
                    logger.error('所有API端点都失败了')
                    raise
                continue

    def get_kline_data(self, symbol: str, interval: str, limit: int = 100, 
                      start_time: int = None, end_time: int = None) -> List[Dict]:
        """
        获取K线数据 - 公开API，无需认证
        
        参数:
        - symbol: 交易对，如 'BTCUSDT'
        - interval: 时间间隔，如 '1m', '5m', '15m', '1h', '4h', '1d'
        - limit: 返回的数据条数，最大1000
        - start_time: 开始时间（毫秒时间戳）
        - end_time: 结束时间（毫秒时间戳）
        """
        endpoint = '/api/v3/klines'
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': min(limit, 1000)  # API限制最大1000
        }
        
        if start_time:
            params['startTime'] = start_time
        if end_time:
            params['endTime'] = end_time
        
        try:
            data = self._make_request(endpoint, params)
            
            klines = []
            for kline in data:
                klines.append({
                    'open_time': datetime.fromtimestamp(kline[0] / 1000),
                    'open': float(kline[1]),
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4]),
                    'volume': float(kline[5]),
                    'close_time': datetime.fromtimestamp(kline[6] / 1000),
                    'quote_volume': float(kline[7]),
                    'trades': int(kline[8]),
                    'taker_buy_base_volume': float(kline[9]),
                    'taker_buy_quote_volume': float(kline[10])
                })
            
            return klines
            
        except Exception as e:
            logger.error(f'获取K线数据失败: {str(e)}')
            raise

    def get_current_price(self, symbol: str) -> float:
        """获取当前价格 - 公开API"""
        endpoint = '/api/v3/ticker/price'
        params = {'symbol': symbol}
        
        try:
            data = self._make_request(endpoint, params)
            return float(data['price'])
        except Exception as e:
            logger.error(f'获取当前价格失败: {str(e)}')
            raise

    def get_all_prices(self) -> List[Dict]:
        """获取所有交易对的当前价格"""
        endpoint = '/api/v3/ticker/price'
        
        try:
            data = self._make_request(endpoint)
            return [{'symbol': item['symbol'], 'price': float(item['price'])} for item in data]
        except Exception as e:
            logger.error(f'获取所有价格失败: {str(e)}')
            raise

    def get_24hr_ticker(self, symbol: str = None) -> Dict:
        """获取24小时行情数据"""
        endpoint = '/api/v3/ticker/24hr'
        params = {'symbol': symbol} if symbol else {}
        
        try:
            data = self._make_request(endpoint, params)
            
            if symbol:
                return self._format_ticker(data)
            else:
                return [self._format_ticker(item) for item in data]
                
        except Exception as e:
            logger.error(f'获取24小时行情失败: {str(e)}')
            raise

    def _format_ticker(self, data: Dict) -> Dict:
        """格式化ticker数据"""
        return {
            'symbol': data['symbol'],
            'price_change': float(data['priceChange']),
            'price_change_percent': float(data['priceChangePercent']),
            'weighted_avg_price': float(data['weightedAvgPrice']),
            'last_price': float(data['lastPrice']),
            'bid_price': float(data['bidPrice']),
            'ask_price': float(data['askPrice']),
            'open_price': float(data['openPrice']),
            'high_price': float(data['highPrice']),
            'low_price': float(data['lowPrice']),
            'volume': float(data['volume']),
            'quote_volume': float(data['quoteVolume']),
            'open_time': datetime.fromtimestamp(data['openTime'] / 1000),
            'close_time': datetime.fromtimestamp(data['closeTime'] / 1000),
            'count': int(data['count'])
        }

    def get_server_time(self) -> datetime:
        """获取服务器时间"""
        endpoint = '/api/v3/time'
        
        try:
            data = self._make_request(endpoint)
            return datetime.fromtimestamp(data['serverTime'] / 1000)
        except Exception as e:
            logger.error(f'获取服务器时间失败: {str(e)}')
            raise

    def get_exchange_info(self) -> Dict:
        """获取交易所信息"""
        endpoint = '/api/v3/exchangeInfo'
        
        try:
            return self._make_request(endpoint)
        except Exception as e:
            logger.error(f'获取交易所信息失败: {str(e)}')
            raise

    def test_connectivity(self) -> bool:
        """测试连接"""
        endpoint = '/api/v3/ping'
        
        try:
            self._make_request(endpoint)
            return True
        except:
            return False
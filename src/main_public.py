#!/usr/bin/env python3
"""
币安公开API数据采集主程序
无需API密钥，使用公开端点获取市场数据
"""

import os
import sys
import asyncio
import signal
import logging
from datetime import datetime
from dotenv import load_dotenv
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from flask import Flask
import threading

from database_sqlite import Database
from binance_public_api import BinancePublicAPI

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 创建logs目录
os.makedirs('logs', exist_ok=True)

# 配置
config = {
    'database': os.getenv('DB_PATH', 'binance_public_data.db'),
    'symbols': [s.strip() for s in os.getenv('SYMBOLS', 'BTCUSDT').split(',')],
    'interval': os.getenv('INTERVAL', '1m'),
    'port': int(os.getenv('PORT', 8080)),
    'collection_interval': int(os.getenv('COLLECTION_INTERVAL', 60)),
    'cleanup_days': int(os.getenv('CLEANUP_DAYS', 30))
}

# 初始化组件
binance_api = BinancePublicAPI()  # 无需API密钥！
database = Database({'database': config['database']})

# Flask应用用于健康检查
app = Flask(__name__)

@app.route('/health')
def health_check():
    return 'OK', 200

@app.route('/status')
def status():
    """获取系统状态"""
    try:
        # 测试API连接
        api_status = binance_api.test_connectivity()
        
        # 获取数据统计
        total_records = 0
        symbol_stats = {}
        
        for symbol in config['symbols']:
            count = asyncio.run(database.get_kline_data_count(symbol))
            symbol_stats[symbol] = count
            total_records += count
        
        return {
            'status': 'running',
            'api_connection': 'ok' if api_status else 'error',
            'total_records': total_records,
            'symbols': symbol_stats,
            'config': {
                'symbols_count': len(config['symbols']),
                'interval': config['interval'],
                'collection_interval': config['collection_interval']
            }
        }
    except Exception as e:
        return {'status': 'error', 'message': str(e)}, 500

async def collect_data_for_symbol(symbol):
    """采集单个货币对的数据"""
    try:
        logger.info(f'📊 开始采集 {symbol} 数据...')
        
        # 获取K线数据（最新2条，确保获取到最新数据）
        kline_data = binance_api.get_kline_data(symbol, config['interval'], 2)
        
        if kline_data:
            await database.insert_kline_data(symbol, kline_data)
            logger.info(f'✅ {symbol} 数据采集完成，时间: {datetime.now().isoformat()}')
            
            # 获取并记录最新数据
            latest = await database.get_latest_kline(symbol)
            if latest:
                logger.info(f'💰 {symbol} 最新价格: ${latest["close_price"]:,.2f}, 时间: {latest["open_time"]}')
        else:
            logger.warning(f'⚠️ {symbol} 没有获取到K线数据')
            
    except Exception as e:
        logger.error(f'❌ {symbol} 数据采集出错: {str(e)}')

async def collect_data():
    """采集所有货币对的数据"""
    logger.info(f'🚀 开始采集 {len(config["symbols"])} 个货币对的数据...')
    
    tasks = []
    for symbol in config['symbols']:
        task = asyncio.create_task(collect_data_for_symbol(symbol))
        tasks.append(task)
        # 避免API频率限制，每个请求间隔200ms
        await asyncio.sleep(0.2)
    
    # 等待所有任务完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计结果
    success_count = sum(1 for r in results if not isinstance(r, Exception))
    error_count = len(results) - success_count
    
    logger.info(f'📈 数据采集完成：成功 {success_count}/{len(config["symbols"])}，失败 {error_count}')

async def clean_old_data():
    """定期清理旧数据"""
    try:
        logger.info('🧹 开始清理旧数据...')
        total_deleted = 0
        for symbol in config['symbols']:
            deleted_count = await database.clear_old_data(symbol, days=config['cleanup_days'])
            total_deleted += deleted_count
            if deleted_count > 0:
                logger.info(f'🗑️ {symbol} 清理了 {deleted_count} 条数据')
        logger.info(f'✅ 清理完成，总共删除了 {total_deleted} 条数据')
    except Exception as e:
        logger.error(f'❌ 清理数据出错: {str(e)}')

def run_flask():
    """在单独的线程中运行Flask"""
    app.run(host='0.0.0.0', port=config['port'], threaded=True)

async def test_api_connection():
    """测试API连接"""
    try:
        logger.info('🔍 测试币安公开API连接...')
        
        # 测试连接
        if binance_api.test_connectivity():
            logger.info('✅ API连接测试成功')
        else:
            logger.error('❌ API连接测试失败')
            return False
            
        # 测试获取服务器时间
        server_time = binance_api.get_server_time()
        logger.info(f'🕒 服务器时间: {server_time}')
        
        # 测试获取BTCUSDT价格
        btc_price = binance_api.get_current_price('BTCUSDT')
        logger.info(f'💰 BTCUSDT当前价格: ${btc_price:,.2f}')
        
        return True
        
    except Exception as e:
        logger.error(f'❌ API连接测试失败: {str(e)}')
        return False

async def main():
    """主函数"""
    try:
        # 测试API连接
        if not await test_api_connection():
            logger.error('API连接失败，程序退出')
            sys.exit(1)
        
        # 连接数据库
        logger.info('🗄️ 正在连接数据库...')
        await database.connect()
        
        # 执行初始数据采集
        logger.info('📊 执行初始数据采集...')
        await collect_data()
        
        # 创建调度器
        scheduler = AsyncIOScheduler()
        
        # 添加定时任务
        scheduler.add_job(
            collect_data, 
            'interval', 
            seconds=config['collection_interval'],
            id='collect_data'
        )
        scheduler.add_job(
            clean_old_data, 
            'cron', 
            hour=2, 
            minute=0,
            id='clean_data'
        )
        
        # 启动调度器
        scheduler.start()
        logger.info(f'⏰ 定时任务已启动，采集间隔: {config["collection_interval"]}秒')
        
        # 在后台线程启动Flask
        flask_thread = threading.Thread(target=run_flask, daemon=True)
        flask_thread.start()
        logger.info(f'🌐 健康检查服务器运行在端口 {config["port"]}')
        
        # 输出服务信息
        logger.info('🎉 币安公开API数据采集服务已启动')
        logger.info(f'📈 货币对数量: {len(config["symbols"])}')
        logger.info(f'💱 收集的货币对: {", ".join(config["symbols"])}')
        logger.info(f'⏱️ 时间间隔: {config["interval"]}')
        logger.info('💡 无需API密钥，使用公开端点')
        logger.info(f'🔗 状态监控: http://localhost:{config["port"]}/status')
        
        # 保持程序运行
        try:
            await asyncio.Event().wait()
        except asyncio.CancelledError:
            pass
            
    except Exception as e:
        logger.error(f'❌ 启动失败: {str(e)}')
        sys.exit(1)
    finally:
        await database.close()

def signal_handler(signum, frame):
    """处理退出信号"""
    logger.info('📴 收到退出信号，正在关闭服务...')
    asyncio.create_task(shutdown())

async def shutdown():
    """关闭服务"""
    await database.close()
    logger.info('✅ 服务已关闭')
    sys.exit(0)

if __name__ == '__main__':
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("🚀 币安公开API数据采集系统")
    print("✅ 无需API密钥")
    print("✅ 使用公开端点")
    print("✅ 自动数据采集")
    print("=" * 40)
    
    # 运行主程序
    asyncio.run(main()) 
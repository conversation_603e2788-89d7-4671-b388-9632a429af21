version: '3.8'

services:
  binance-public-api:
    build:
      context: .
      dockerfile: Dockerfile.public
    container_name: binance-public-data-collector
    restart: unless-stopped
    ports:
      - "8081:8080"  # 健康检查和API端口
      - "5002:5001"  # Web监控界面端口
    volumes:
      - ./data:/app/data          # 数据持久化
      - ./logs:/app/logs          # 日志持久化
    environment:
      - DB_TYPE=sqlite
      - DB_PATH=/app/data/binance_public_data.db
      - SYMBOLS=BTCUSDT,ETHUSDT,BNBUSDT,ADAUSDT,SOLUSDT,XRPUSDT,DOTUSDT,DOGEUSDT,AVAXUSDT,MATICUSDT
      - INTERVAL=1m
      - PORT=8080
      - LOG_LEVEL=INFO
      - COLLECTION_INTERVAL=60
      - CLEANUP_DAYS=30
      - PUBLIC_API_MODE=true
    networks:
      - binance-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=false"
      - "description=币安公开API数据采集系统"
      - "version=1.0"

networks:
  binance-network:
    driver: bridge
    name: binance-public-network

volumes:
  binance-data:
    driver: local
    name: binance-public-data 
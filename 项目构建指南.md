# 币安公开API数据采集系统 - 项目构建指南

## 📋 项目概述

这是一个基于Python和Docker的币安公开API数据采集系统，具有以下特点：
- 🔓 **无需API密钥** - 使用币安公开端点获取市场数据
- 🐳 **Docker容器化** - 完全容器化部署，环境隔离
- 📊 **实时监控** - Web界面实时展示价格图表
- 💾 **数据持久化** - 使用SQLite存储，数据不丢失
- 🔄 **自动采集** - 定时采集10种主流加密货币数据

## 🏗️ 项目结构解析

```
📁 Data Feed - ClaudeCode - Python/
├── 📄 control.sh                    # 主控制脚本（启动/停止/状态查看）
├── 📄 docker-compose.public.yml     # Docker Compose配置
├── 📄 Dockerfile.public             # Docker镜像构建文件
├── 📄 quick-docker-start.sh         # 快速启动脚本
├── 📄 README.md                     # 项目说明文档
├── 📄 requirements.txt              # Python依赖列表
├── 📄 web_dashboard_public.py       # Web仪表板主程序
├── 📁 data/                         # 数据目录（持久化）
│   └── 📄 binance_public_data.db    # SQLite数据库文件
├── 📁 logs/                         # 日志目录
│   └── 📄 app.log                   # 应用日志文件
├── 📁 src/                          # 源代码目录
│   ├── 📄 binance_public_api.py     # 币安公开API客户端
│   ├── 📄 database_sqlite.py        # SQLite数据库操作类
│   └── 📄 main_public.py            # 主程序入口
└── 📁 templates/                    # Web模板目录
    └── 📄 dashboard_public.html     # 仪表板HTML模板
```

### 核心模块说明

1. **数据采集模块** (`src/binance_public_api.py`)
   - 使用币安公开API获取实时价格数据
   - 支持多种加密货币符号
   - 异步请求，高效数据采集

2. **数据存储模块** (`src/database_sqlite.py`)
   - SQLite数据库操作封装
   - 数据表创建和管理
   - 数据查询和统计功能

3. **主控制程序** (`src/main_public.py`)
   - 定时任务调度（APScheduler）
   - 数据采集流程控制
   - 健康检查API服务

4. **Web仪表板** (`web_dashboard_public.py`)
   - Flask Web应用
   - 实时数据可视化
   - RESTful API接口

## 🚀 一步一步构建指南

### 步骤1: 环境准备

#### 1.1 系统要求
```bash
# 确认操作系统
uname -a

# 推荐配置
- macOS 10.15+ / Ubuntu 18.04+ / Windows 10+
- 内存: 2GB+
- 硬盘: 5GB+ 可用空间
```

#### 1.2 安装Docker和Docker Compose
```bash
# macOS (使用Homebrew)
brew install docker docker-compose

# 或者下载Docker Desktop
# https://www.docker.com/products/docker-desktop

# 验证安装
docker --version
docker-compose --version
```

#### 1.3 克隆或下载项目
```bash
# 如果从Git仓库克隆
git clone <repository-url>
cd "Data Feed - ClaudeCode - Python"

# 或者直接在现有目录工作
cd "/Users/<USER>/Desktop/Desktop Cursor/Data Feed - ClaudeCode - Python"
```

### 步骤2: 项目配置

#### 2.1 检查项目文件完整性
```bash
# 验证关键文件存在
ls -la control.sh
ls -la docker-compose.public.yml
ls -la Dockerfile.public
ls -la requirements.txt
```

#### 2.2 创建必要目录
```bash
# 创建数据和日志目录（如果不存在）
mkdir -p data logs
chmod 755 data logs
```

#### 2.3 设置执行权限
```bash
# 给控制脚本执行权限
chmod +x control.sh
chmod +x quick-docker-start.sh
```

### 步骤3: 构建Docker镜像

#### 3.1 构建镜像
```bash
# 使用Docker Compose构建
docker-compose -f docker-compose.public.yml build

# 或者手动构建
docker build -f Dockerfile.public -t binance-public-data .
```

#### 3.2 验证镜像构建
```bash
# 查看构建的镜像
docker images | grep binance
```

### 步骤4: 启动系统

#### 4.1 使用控制脚本启动（推荐）
```bash
# 一键启动
./control.sh start

# 输出示例:
# 🚀 启动币安数据采集系统...
# Creating binance-public-data-collector ... done
# ✅ 启动完成！
# 🌐 Web界面: http://localhost:5002
# 📊 API状态: http://localhost:8081/status
```

#### 4.2 手动启动
```bash
# 使用Docker Compose启动
docker-compose -f docker-compose.public.yml up -d
```

### 步骤5: 验证部署

#### 5.1 检查容器状态
```bash
# 查看运行状态
./control.sh status

# 或者使用Docker命令
docker ps | grep binance-public-data-collector
```

#### 5.2 测试Web接口
```bash
# 测试健康检查
curl http://localhost:8081/health

# 测试API状态
curl http://localhost:8081/status

# 浏览器访问Web界面
open http://localhost:5002
```

#### 5.3 查看实时日志
```bash
# 查看应用日志
./control.sh logs

# 或者查看文件日志
tail -f logs/app.log
```

### 步骤6: 数据验证

#### 6.1 检查数据库
```bash
# 进入容器查看数据库
docker exec -it binance-public-data-collector sqlite3 /app/data/binance_public_data.db

# SQLite命令
.tables
SELECT COUNT(*) FROM market_data;
SELECT * FROM market_data LIMIT 5;
.quit
```

#### 6.2 验证数据采集
```bash
# 查看最新数据
curl http://localhost:5002/api/latest

# 查看统计信息
./control.sh data
```

## 🔧 系统管理

### 日常操作命令
```bash
# 启动系统
./control.sh start

# 停止系统
./control.sh stop

# 重启系统
./control.sh restart

# 查看状态
./control.sh status

# 查看日志
./control.sh logs

# 健康检查
./control.sh health

# 数据统计
./control.sh data
```

### 数据管理
```bash
# 数据备份
cp data/binance_public_data.db data/backup_$(date +%Y%m%d_%H%M%S).db

# 清理旧日志
find logs/ -name "*.log" -mtime +7 -delete
```

## 🌐 访问地址

| 服务类型 | 访问地址 | 说明 |
|---------|----------|------|
| **Web监控界面** | http://localhost:5002 | 实时价格图表和历史数据 |
| **API状态** | http://localhost:8081/status | 系统状态JSON响应 |
| **健康检查** | http://localhost:8081/health | 服务健康状态检查 |
| **最新数据API** | http://localhost:5002/api/latest | 最新价格数据API |

## 📊 监控的加密货币

系统默认监控以下10种主流加密货币：

1. **BTCUSDT** - 比特币/美元
2. **ETHUSDT** - 以太坊/美元
3. **BNBUSDT** - 币安币/美元
4. **ADAUSDT** - 卡尔达诺/美元
5. **SOLUSDT** - Solana/美元
6. **XRPUSDT** - 瑞波币/美元
7. **DOTUSDT** - 波卡/美元
8. **DOGEUSDT** - 狗狗币/美元
9. **AVAXUSDT** - 雪崩/美元
10. **MATICUSDT** - Polygon/美元

## ⚙️ 配置参数

可以在 `docker-compose.public.yml` 中修改以下环境变量：

```yaml
environment:
  - DB_TYPE=sqlite                    # 数据库类型
  - DB_PATH=/app/data/binance_public_data.db  # 数据库路径
  - SYMBOLS=BTCUSDT,ETHUSDT,BNBUSDT... # 监控的交易对
  - INTERVAL=1m                       # 数据间隔
  - COLLECTION_INTERVAL=60            # 采集间隔（秒）
  - CLEANUP_DAYS=30                   # 数据保留天数
  - LOG_LEVEL=INFO                    # 日志级别
```

## 🐛 故障排除

### 常见问题及解决方案

1. **Docker未启动**
   ```bash
   # 启动Docker Desktop或Docker服务
   sudo systemctl start docker  # Linux
   open -a Docker               # macOS
   ```

2. **端口被占用**
   ```bash
   # 查看端口使用情况
   lsof -i :5002
   lsof -i :8081
   
   # 修改docker-compose.public.yml中的端口映射
   ```

3. **权限问题**
   ```bash
   # 修复目录权限
   sudo chown -R $USER:$USER data logs
   chmod 755 data logs
   ```

4. **容器启动失败**
   ```bash
   # 查看详细错误日志
   docker-compose -f docker-compose.public.yml logs
   
   # 重新构建镜像
   docker-compose -f docker-compose.public.yml build --no-cache
   ```

## 📈 系统监控

### 性能指标
- **数据采集频率**: 每60秒一次
- **数据存储**: SQLite本地存储
- **内存使用**: ~100-200MB
- **CPU使用**: ~5-10%

### 数据统计
```bash
# 查看数据量统计
./control.sh data

# 输出示例:
# 📊 数据统计:
# - 总记录数: 1,234
# - 最新记录时间: 2025-06-18 15:30:00
# - 数据库文件大小: 2.5MB
```

## 🔧 开发和自定义

### 本地开发环境
```bash
# 安装Python依赖（可选，用于本地开发）
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 本地运行（需要先配置数据库）
python src/main_public.py
python web_dashboard_public.py
```

### 添加新的交易对
1. 修改 `docker-compose.public.yml` 中的 `SYMBOLS` 环境变量
2. 重启容器：`./control.sh restart`

### 修改采集频率
1. 修改 `COLLECTION_INTERVAL` 环境变量（秒）
2. 重启容器应用配置

## 📝 注意事项

1. **数据持久化**: 数据存储在 `./data/` 目录，容器重启不会丢失
2. **网络要求**: 需要能够访问币安公开API（api.binance.com）
3. **资源使用**: 建议定期清理旧数据，避免数据库文件过大
4. **安全考虑**: 仅使用公开API，无需担心API密钥泄露

## 🎯 下一步

项目启动成功后，你可以：

1. 🌐 访问 **http://localhost:5002** 查看实时数据仪表板
2. 📊 监控 **http://localhost:8081/status** 了解系统状态
3. 📈 通过API接口获取数据进行二次开发
4. 🔧 根据需求自定义监控的交易对和参数

---

**🎉 恭喜！** 你的币安公开API数据采集系统已经成功构建并运行！

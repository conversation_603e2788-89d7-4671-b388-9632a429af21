# Binance API Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
BINANCE_API_URL=https://api.binance.com

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=biFinance
DB_NAME=binance_data

# Trading Configuration - 支持多个货币对，用逗号分隔
SYMBOLS=BTCUSDT,ETHUSDT,BNBUSDT,ADAUSDT,SOLUSDT,XRPUSDT,DOTUSDT,DOGEUSDT,AVAXUSDT,MATICUSDT
INTERVAL=1m

# 历史数据回填配置
BACKFILL_DAYS=14

# Server Configuration
PORT=8080

# Logging Configuration
LOG_LEVEL=INFO
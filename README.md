# 币安公开API数据采集系统

> 🚀 **无需API密钥**的币安公开数据采集系统，采用Docker容器化部署

## ✨ 特色功能

- ✅ **无需API密钥** - 使用币安公开端点
- ✅ **容器化部署** - 一键启动，环境隔离  
- ✅ **实时监控** - Web界面实时展示数据
- ✅ **自动采集** - 每60秒自动采集10种主流币种
- ✅ **数据持久化** - 容器重启数据不丢失

## 🚀 快速启动

```bash
# 一键启动
./control.sh start

# 查看状态
./control.sh status

# 查看帮助
./control.sh help
```

## 🔗 访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| **Web监控界面** | http://localhost:5002 | 实时价格图表 |
| **API状态** | http://localhost:8081/status | 系统状态JSON |
| **健康检查** | http://localhost:8081/health | 服务健康状态 |

## 📊 监控币种

- **BTCUSDT** (比特币) | **ETHUSDT** (以太坊) | **BNBUSDT** (币安币)
- **ADAUSDT** (卡尔达诺) | **SOLUSDT** (Solana) | **XRPUSDT** (瑞波币)  
- **DOTUSDT** (波卡) | **DOGEUSDT** (狗狗币) | **AVAXUSDT** (雪崩)
- **MATICUSDT** (Polygon)

## 🎮 控制命令

```bash
./control.sh start      # 启动系统
./control.sh stop       # 停止系统  
./control.sh restart    # 重启系统
./control.sh status     # 查看状态
./control.sh logs       # 查看日志
./control.sh health     # 健康检查
./control.sh data       # 数据统计
./control.sh web        # 打开Web界面
```

## 📁 项目结构

```
├── src/                          # 核心源代码
│   ├── main_public.py           # 主程序
│   ├── binance_public_api.py    # 币安公开API
│   └── database_sqlite.py       # SQLite数据库
├── templates/                   # Web模板
│   └── dashboard_public.html    # 仪表板模板
├── data/                        # 数据存储
├── logs/                        # 日志文件
├── control.sh                   # 系统控制脚本
├── web_dashboard_public.py      # Web仪表板
├── docker-compose.public.yml    # Docker编排
└── Dockerfile.public           # Docker镜像
```

## 🛠️ 环境要求

- Docker
- Docker Compose
- Python 3.11+ (容器内)

---

**🎉 享受您的数据采集之旅！** 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>币安公开API数据监控</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .badge { background: #4CAF50; padding: 5px 10px; border-radius: 15px; font-size: 12px; margin-left: 10px; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .status-bar { background: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .price { font-size: 24px; font-weight: bold; margin: 10px 0; }
        .change { font-size: 16px; font-weight: bold; }
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .loading { text-align: center; padding: 50px; color: #666; }
        .error { background: #f44336; color: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #4CAF50; color: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 币安公开API数据监控</h1>
        <span class="badge">无需API密钥</span>
        <span class="badge">实时数据</span>
        <span class="badge">公开端点</span>
    </div>

    <div class="container">
        <div class="status-bar">
            <div id="status">🔄 正在加载...</div>
        </div>

        <div class="grid" id="priceGrid">
            <div class="loading">正在加载价格数据...</div>
        </div>

        <div class="chart-container">
            <h3>📊 价格趋势图</h3>
            <canvas id="priceChart"></canvas>
        </div>
    </div>

    <script>
        let chart;
        
        async function fetchData() {
            try {
                const response = await axios.get('/api/data');
                if (response.data.success) {
                    updatePrices(response.data.data.latest_prices);
                    updateStatus(response.data.data);
                } else {
                    showError('获取数据失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        function updatePrices(prices) {
            const grid = document.getElementById('priceGrid');
            grid.innerHTML = '';
            
            for (const [symbol, data] of Object.entries(prices)) {
                const card = document.createElement('div');
                card.className = 'card';
                
                const changeClass = data.change >= 0 ? 'positive' : 'negative';
                const changeSymbol = data.change >= 0 ? '+' : '';
                
                card.innerHTML = `
                    <h3>${symbol}</h3>
                    <div class="price">$${data.price.toLocaleString()}</div>
                    <div class="change ${changeClass}">
                        ${changeSymbol}${data.change_percent.toFixed(2)}%
                    </div>
                    <small>更新时间: ${new Date(data.time).toLocaleTimeString()}</small>
                `;
                
                grid.appendChild(card);
            }
        }
        
        function updateStatus(data) {
            const statusDiv = document.getElementById('status');
            const totalSymbols = Object.keys(data.latest_prices).length;
            const updateTime = new Date(data.timestamp).toLocaleString();
            
            statusDiv.innerHTML = `
                ✅ 系统正常运行 | 
                📊 监控 ${totalSymbols} 个货币对 | 
                🕒 更新时间: ${updateTime} |
                💡 使用公开API端点
            `;
        }
        
        function showError(message) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `❌ ${message}`;
        }
        
        function initChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }
        
        // 初始化
        initChart();
        fetchData();
        
        // 定时更新
        setInterval(fetchData, 5000);
    </script>
</body>
</html>
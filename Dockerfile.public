FROM python:3.11-slim
WORKDIR /app
ENV PYTHONUNBUFFERED=1
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*
RUN pip install python-dotenv requests flask APScheduler
COPY src/ /app/src/
COPY web_dashboard_public.py /app/
COPY .env /app/
EXPOSE 8080 5001
RUN echo "#!/bin/bash" > /app/start.sh && \
    echo "python src/main_public.py &" >> /app/start.sh && \
    echo "sleep 5" >> /app/start.sh && \
    echo "python web_dashboard_public.py &" >> /app/start.sh && \
    echo "wait" >> /app/start.sh && \
    chmod +x /app/start.sh
CMD ["/app/start.sh"]

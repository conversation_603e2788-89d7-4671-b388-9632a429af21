#!/bin/bash

# 币安公开API数据采集系统控制面板
# 使用方法: ./control.sh [start|stop|restart|status|logs|health]

PROJECT_DIR="/Users/<USER>/Desktop/Desktop Cursor/Data Feed - ClaudeCode - Python"
COMPOSE_FILE="docker-compose.public.yml"

# 确保在正确的目录中
cd "$PROJECT_DIR" || {
    echo "❌ 无法切换到项目目录: $PROJECT_DIR"
    exit 1
}

case "${1:-help}" in
    "start")
        echo "🚀 启动币安数据采集系统..."
        docker-compose -f $COMPOSE_FILE up -d
        sleep 5
        echo "✅ 启动完成！"
        echo "🌐 Web界面: http://localhost:5002"
        echo "📊 API状态: http://localhost:8081/status"
        ;;
    
    "stop")
        echo "🛑 停止币安数据采集系统..."
        docker-compose -f $COMPOSE_FILE down
        echo "✅ 系统已停止"
        ;;
    
    "restart")
        echo "🔄 重启币安数据采集系统..."
        docker-compose -f $COMPOSE_FILE restart
        echo "✅ 系统已重启"
        ;;
    
    "status")
        echo "📊 系统状态:"
        echo "----------------------------------------"
        docker ps --filter name=binance-public-data-collector
        echo ""
        echo "📈 API状态:"
        curl -s http://localhost:5002/api/status | python -m json.tool 2>/dev/null || echo "❌ API不可访问"
        ;;
    
    "logs")
        echo "📋 查看系统日志 (按Ctrl+C退出):"
        docker-compose -f $COMPOSE_FILE logs -f
        ;;
    
    "health")
        echo "❤️ 健康检查:"
        echo "----------------------------------------"
        echo -n "数据采集服务: "
        if curl -s http://localhost:8081/health >/dev/null 2>&1; then
            echo "✅ 正常"
        else
            echo "❌ 异常"
        fi
        
        echo -n "Web监控界面: "
        if curl -s http://localhost:5002/api/status >/dev/null 2>&1; then
            echo "✅ 正常"
        else
            echo "❌ 异常"
        fi
        ;;
    
    "build")
        echo "🏗️ 重新构建Docker镜像..."
        docker-compose -f $COMPOSE_FILE build
        echo "✅ 构建完成"
        ;;
    
    "clean")
        echo "🧹 清理系统..."
        docker-compose -f $COMPOSE_FILE down -v
        docker rmi datafeed-claudecode-python-binance-public-api 2>/dev/null || true
        echo "✅ 清理完成"
        ;;
    
    "web")
        echo "🌐 打开Web监控界面..."
        open http://localhost:5002 2>/dev/null || {
            echo "请手动打开: http://localhost:5002"
        }
        ;;
    
    "data")
        echo "💾 数据库统计:"
        echo "----------------------------------------"
        if [ -f "data/binance_public_data.db" ]; then
            python3 -c "
import sqlite3
try:
    conn = sqlite3.connect('data/binance_public_data.db')
    cursor = conn.cursor()
    cursor.execute('SELECT COUNT(*) FROM kline_data')
    total = cursor.fetchone()[0]
    print(f'📊 总记录数: {total}')
    print('📈 各货币对统计:')
    cursor.execute('SELECT symbol, COUNT(*) FROM kline_data GROUP BY symbol ORDER BY COUNT(*) DESC')
    for row in cursor.fetchall():
        print(f'  {row[0]}: {row[1]:,} 条')
    conn.close()
except Exception as e:
    print(f'❌ 无法读取数据库: {e}')
"
        else
            echo "❌ 数据库文件不存在"
        fi
        ;;
    
    "help"|*)
        echo "🎮 币安公开API数据采集系统控制面板"
        echo "=================================="
        echo "使用方法: ./control.sh [命令]"
        echo ""
        echo "可用命令:"
        echo "  start    - 启动系统"
        echo "  stop     - 停止系统"
        echo "  restart  - 重启系统"
        echo "  status   - 查看状态"
        echo "  logs     - 查看日志"
        echo "  health   - 健康检查"
        echo "  build    - 重新构建"
        echo "  clean    - 清理系统"
        echo "  web      - 打开Web界面"
        echo "  data     - 数据统计"
        echo "  help     - 显示帮助"
        echo ""
        echo "🔗 快速访问:"
        echo "  Web界面: http://localhost:5002"
        echo "  API状态: http://localhost:8081/status"
        echo "  健康检查: http://localhost:8081/health"
        ;;
esac 
#!/usr/bin/env python3
"""
币安公开API数据可视化仪表板
显示从公开API采集的实时数据
"""

import sqlite3
import json
from flask import Flask, render_template, jsonify
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = Flask(__name__)

# 配置
DB_PATH = os.getenv('DB_PATH', 'binance_public_data.db')
SYMBOLS = os.getenv('SYMBOLS', 'BTCUSDT,ETHUSDT,BNBUSDT,ADAUSDT,SOLUSDT').split(',')

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def get_latest_prices():
    """获取最新价格"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    latest_prices = {}
    
    for symbol in SYMBOLS:
        cursor.execute("""
            SELECT close_price, open_time, 
                   (close_price - open_price) as price_change,
                   ((close_price - open_price) / open_price * 100) as change_percent
            FROM kline_data 
            WHERE symbol = ? 
            ORDER BY open_time DESC 
            LIMIT 1
        """, (symbol,))
        
        result = cursor.fetchone()
        if result:
            latest_prices[symbol] = {
                'price': float(result['close_price']),
                'change': float(result['price_change']),
                'change_percent': float(result['change_percent']),
                'time': result['open_time']
            }
    
    conn.close()
    return latest_prices

def get_chart_data(symbol, hours=24):
    """获取图表数据"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=hours)
    
    cursor.execute("""
        SELECT open_time, open_price, high_price, low_price, close_price, volume
        FROM kline_data 
        WHERE symbol = ? AND open_time >= ? AND open_time <= ?
        ORDER BY open_time ASC
    """, (symbol, start_time.isoformat(), end_time.isoformat()))
    
    results = cursor.fetchall()
    conn.close()
    
    return [{
        'time': row['open_time'],
        'open': float(row['open_price']),
        'high': float(row['high_price']),
        'low': float(row['low_price']),
        'close': float(row['close_price']),
        'volume': float(row['volume'])
    } for row in results]

def get_statistics():
    """获取统计信息"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    stats = {}
    
    for symbol in SYMBOLS:
        # 总记录数
        cursor.execute("SELECT COUNT(*) as count FROM kline_data WHERE symbol = ?", (symbol,))
        count = cursor.fetchone()['count']
        
        # 最早和最新记录时间
        cursor.execute("""
            SELECT MIN(open_time) as earliest, MAX(open_time) as latest 
            FROM kline_data WHERE symbol = ?
        """, (symbol,))
        time_range = cursor.fetchone()
        
        stats[symbol] = {
            'count': count,
            'earliest': time_range['earliest'],
            'latest': time_range['latest']
        }
    
    conn.close()
    return stats

@app.route('/')
def index():
    """主页"""
    return render_template('dashboard_public.html', symbols=SYMBOLS)

@app.route('/api/data')
def api_data():
    """API数据端点"""
    try:
        latest_prices = get_latest_prices()
        stats = get_statistics()
        
        return jsonify({
            'success': True,
            'data': {
                'latest_prices': latest_prices,
                'statistics': stats,
                'symbols': SYMBOLS,
                'timestamp': datetime.now().isoformat()
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/chart/<symbol>')
def api_chart(symbol):
    """图表数据API"""
    try:
        hours = int(request.args.get('hours', 24))
        chart_data = get_chart_data(symbol, hours)
        
        return jsonify({
            'success': True,
            'symbol': symbol,
            'data': chart_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/status')
def api_status():
    """系统状态API"""
    try:
        # 检查数据库是否存在
        db_exists = os.path.exists(DB_PATH)
        
        if not db_exists:
            return jsonify({
                'success': False,
                'status': 'no_data',
                'message': '数据库文件不存在，请先启动数据采集服务'
            })
        
        # 获取数据统计
        stats = get_statistics()
        total_records = sum(stat['count'] for stat in stats.values())
        
        return jsonify({
            'success': True,
            'status': 'running',
            'database': {
                'path': DB_PATH,
                'exists': db_exists,
                'total_records': total_records
            },
            'symbols': stats,
            'mode': 'public_api'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 创建模板
def create_template():
    """创建HTML模板"""
    os.makedirs('templates', exist_ok=True)
    
    template_content = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>币安公开API数据监控</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .badge { background: #4CAF50; padding: 5px 10px; border-radius: 15px; font-size: 12px; margin-left: 10px; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .status-bar { background: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .price { font-size: 24px; font-weight: bold; margin: 10px 0; }
        .change { font-size: 16px; font-weight: bold; }
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .loading { text-align: center; padding: 50px; color: #666; }
        .error { background: #f44336; color: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #4CAF50; color: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 币安公开API数据监控</h1>
        <span class="badge">无需API密钥</span>
        <span class="badge">实时数据</span>
        <span class="badge">公开端点</span>
    </div>

    <div class="container">
        <div class="status-bar">
            <div id="status">🔄 正在加载...</div>
        </div>

        <div class="grid" id="priceGrid">
            <div class="loading">正在加载价格数据...</div>
        </div>

        <div class="chart-container">
            <h3>📊 价格趋势图</h3>
            <canvas id="priceChart"></canvas>
        </div>
    </div>

    <script>
        let chart;
        
        async function fetchData() {
            try {
                const response = await axios.get('/api/data');
                if (response.data.success) {
                    updatePrices(response.data.data.latest_prices);
                    updateStatus(response.data.data);
                } else {
                    showError('获取数据失败');
                }
            } catch (error) {
                showError('网络错误: ' + error.message);
            }
        }
        
        function updatePrices(prices) {
            const grid = document.getElementById('priceGrid');
            grid.innerHTML = '';
            
            for (const [symbol, data] of Object.entries(prices)) {
                const card = document.createElement('div');
                card.className = 'card';
                
                const changeClass = data.change >= 0 ? 'positive' : 'negative';
                const changeSymbol = data.change >= 0 ? '+' : '';
                
                card.innerHTML = `
                    <h3>${symbol}</h3>
                    <div class="price">$${data.price.toLocaleString()}</div>
                    <div class="change ${changeClass}">
                        ${changeSymbol}${data.change_percent.toFixed(2)}%
                    </div>
                    <small>更新时间: ${new Date(data.time).toLocaleTimeString()}</small>
                `;
                
                grid.appendChild(card);
            }
        }
        
        function updateStatus(data) {
            const statusDiv = document.getElementById('status');
            const totalSymbols = Object.keys(data.latest_prices).length;
            const updateTime = new Date(data.timestamp).toLocaleString();
            
            statusDiv.innerHTML = `
                ✅ 系统正常运行 | 
                📊 监控 ${totalSymbols} 个货币对 | 
                🕒 更新时间: ${updateTime} |
                💡 使用公开API端点
            `;
        }
        
        function showError(message) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `❌ ${message}`;
        }
        
        function initChart() {
            const ctx = document.getElementById('priceChart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }
        
        // 初始化
        initChart();
        fetchData();
        
        // 定时更新
        setInterval(fetchData, 5000);
    </script>
</body>
</html>
    '''
    
    with open('templates/dashboard_public.html', 'w', encoding='utf-8') as f:
        f.write(template_content.strip())

if __name__ == '__main__':
    # 创建模板文件
    create_template()
    
    print("🌐 启动币安公开API数据可视化仪表板...")
    print(f"📊 数据库: {DB_PATH}")
    print(f"🔗 访问地址: http://localhost:5001")
    print(f"📈 监控货币对: {', '.join(SYMBOLS)}")
    print("💡 使用公开API数据，无需认证")
    print("按 Ctrl+C 停止服务")
    
    app.run(host='0.0.0.0', port=5001, debug=False) 
#!/bin/bash

# 快速启动脚本 - 币安公开API Docker容器

echo "🚀 快速启动币安公开API数据采集系统"
echo "✅ 无需API密钥"
echo "✅ 使用Docker容器"
echo "================================"

# 停止可能存在的本地服务
echo "🛑 停止本地服务..."
pkill -f "main_public.py" 2>/dev/null || true
pkill -f "web_dashboard_public.py" 2>/dev/null || true

# 创建数据目录
echo "📁 创建数据目录..."
mkdir -p data logs

# 构建并启动容器
echo "🏗️ 构建Docker镜像..."
docker-compose -f docker-compose.public.yml build

echo "🚀 启动容器..."
docker-compose -f docker-compose.public.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -s http://localhost:8081/health > /dev/null; then
    echo "✅ 数据采集服务正常"
else
    echo "❌ 数据采集服务异常"
fi

if curl -s http://localhost:5002/api/status > /dev/null; then
    echo "✅ Web界面服务正常"
else
    echo "❌ Web界面服务异常"
fi

echo "================================"
echo "🎉 启动完成！"
echo "🌐 Web界面: http://localhost:5002"
echo "📊 API状态: http://localhost:8081/status"
echo "❤️ 健康检查: http://localhost:8081/health"
echo "================================"
echo ""
echo "常用命令："
echo "  查看日志: docker-compose -f docker-compose.public.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.public.yml down"
echo "  重启服务: docker-compose -f docker-compose.public.yml restart" 